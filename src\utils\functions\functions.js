import DOMPurify from "dompurify";

export const sanitizeAndTruncate = (htmlString, wordLimit) => {
  if (!htmlString) return "";
  const cleanHtml = DOMPurify.sanitize(htmlString);
  const tempDiv = document.createElement("div");
  tempDiv.innerHTML = cleanHtml;
  const textOnly = tempDiv.textContent || tempDiv.innerText || "";
  const words = textOnly.split(" ");
  return words.length > wordLimit
    ? words.slice(0, wordLimit).join(" ") + "..."
    : textOnly;
};

export function filteredBlogs(blogs, searchTerm) {
  return blogs.filter((blog) =>
    blog.title.toLowerCase().includes(searchTerm.toLowerCase())
  );
}

export const sanitizeHtml = (htmlString) => {
  if (!htmlString) return "";
  const cleanHtml = DOMPurify.sanitize(htmlString);
  const tempDiv = document.createElement("div");
  tempDiv.innerHTML = cleanHtml;
  return tempDiv.textContent || tempDiv.innerText || "";
};
