import React, { useState, useEffect, useRef } from "react";
import { socalMedia } from "../../footer/consts";
import Link from "../../footer/Link";
import { sanitizeHtml } from "../../../utils/functions/functions";

export default function Body({ article }) {
  const [activeSection, setActiveSection] = useState("null");

  const sectionRefs = {
    Introduction: useRef(null),
    ProteinRecovery: useRef(null),
    Strategies: useRef(null),
    Reference: useRef(null),
  };

  const handleScrollTo = (section) => {
    sectionRefs[section]?.current?.scrollIntoView({ behavior: "smooth" });
    setActiveSection(section);
  };

  useEffect(() => {
    const handleScroll = () => {
      const scrollY = window.scrollY + 150;

      for (const section in sectionRefs) {
        const ref = sectionRefs[section].current;
        if (ref) {
          const top = ref.offsetTop;
          const height = ref.offsetHeight;
          if (scrollY >= top && scrollY < top + height) {
            setActiveSection(section);
            break;
          }
        }
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const navItemClass = (section) =>
    `flex justify-between items-center cursor-pointer hover:text-blue-400 ${
      activeSection === section ? "text-blue-500 font-bold" : ""
    }`;

  const handleShare = (link) => {
    const currentUrl = encodeURIComponent(window.location.href);
    const text = encodeURIComponent("Check out this article!");
    let shareUrl = "";

    if (link.includes("facebook.com")) {
      shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${currentUrl}`;
    } else if (link.includes("twitter.com")) {
      shareUrl = `https://twitter.com/intent/tweet?url=${currentUrl}&text=${text}`;
    } else if (link.includes("linkedin.com")) {
      shareUrl = `https://www.linkedin.com/shareArticle?mini=true&url=${currentUrl}&title=${text}`;
    } else if (link.includes("instagram.com")) {
      alert(
        "Instagram doesn’t support direct web share links — please copy the link manually."
      );
      return;
    } else {
      shareUrl = link;
    }

    window.open(shareUrl, "_blank");
  };

  console.log(article);

  return (
    <div className="min-h-screen text-grey px-4 sm:px-6 md:px-16 py-10">
      <div className="flex flex-col lg:flex-row gap-10">
        <div className="lg:w-1/4 space-y-6 text-base sticky top-24">
          <div
            onClick={() => handleScrollTo("Introduction")}
            className={navItemClass("Introduction")}
          >
            <span>Introduction →</span>
          </div>
          <div
            onClick={() => handleScrollTo("ProteinRecovery")}
            className={navItemClass("ProteinRecovery")}
          >
            <span>Protein recovery from inclusion bodies → </span>
          </div>
          <div
            onClick={() => handleScrollTo("Strategies")}
            className={navItemClass("Strategies")}
          >
            <span>Strategies to minimize inclusion bodies →</span>
          </div>
          <div
            onClick={() => handleScrollTo("Reference")}
            className={navItemClass("Reference")}
          >
            <span>Reference →</span>
          </div>
          <div className="h-px bg-black w-64 my-4"></div>

          <div className="flex space-x-4 pt-6">
            {socalMedia.map((media, index) => (
              <div
                key={index}
                className="cursor-pointer"
                onClick={() => handleShare(media.link)}
                title={typeof media.title === "string" ? media.title : ""}
              >
                {media.title}
              </div>
            ))}
          </div>
        </div>

        <div className="lg:w-3/4 space-y-6 text-justify text-[15px] leading-relaxed">
          <div ref={sectionRefs.Introduction}>
            <div
              dangerouslySetInnerHTML={{ __html: sanitizeHtml(article.para1) }}
            />
            <div className="flex justify-center my-6">
              <img
                src="/images/Figure 5.png"
                alt="Inclusion bodies"
                className="max-w-full h-auto"
              />
            </div>
          </div>

          <div ref={sectionRefs.ProteinRecovery}>
            {article.para2 && (
              <div
                dangerouslySetInnerHTML={{
                  __html: sanitizeHtml(article.para2),
                }}
              />
            )}
            {article.para2 && (
              <div className="flex justify-center my-6">
                <img
                  src="/images/Figure 6.png"
                  alt="Inclusion bodies"
                  className="max-w-full h-auto"
                />
              </div>
            )}
            {article.para3 && (
              <div
                dangerouslySetInnerHTML={{
                  __html: sanitizeHtml(article.para3),
                }}
              />
            )}
            {article.para3 && (
              <div className="flex justify-center my-6">
                <img
                  src="/images/Frame 207.png"
                  alt="Inclusion bodies"
                  className="max-w-full h-auto"
                />
              </div>
            )}
          </div>

          {article.para4 && (
            <div ref={sectionRefs.Strategies}>
              <div
                dangerouslySetInnerHTML={{
                  __html: sanitizeHtml(article.para4),
                }}
              />
            </div>
          )}

          {article.para5 && (
            <div ref={sectionRefs.Strategies}>
              <div
                dangerouslySetInnerHTML={{
                  __html: sanitizeHtml(article.para5),
                }}
              />
              <div className="flex justify-center my-6">
                <img
                  src="/images/Frame 208.png"
                  alt="Inclusion bodies"
                  className="max-w-full h-auto"
                />
              </div>
            </div>
          )}

          {article.para6 && (
            <div
              dangerouslySetInnerHTML={{ __html: sanitizeHtml(article.para6) }}
            />
          )}

          <div ref={sectionRefs.Reference}>
            <h2 className="text-2xl font-bold mb-2">Reference:</h2>
            <p>
              Bhatwa, A., Wang, W., Hassan, Y. I., Abraham, N., Li, X. Z., &
              Zhou, T. (2021)...
            </p>
            <p>García-Fruitós, E. (2010). Inclusion bodies: A new concept...</p>
            <p>
              Jäger, V. D., Lamm, R., Küsters, K., Ölçücü, G., Oldiges, M.,
              Jaeger, K.-E., Büchs, J., & Krauss, U. (2020)...
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
