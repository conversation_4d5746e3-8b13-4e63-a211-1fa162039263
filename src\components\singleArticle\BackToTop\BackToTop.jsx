import React, { useState, useEffect } from "react";

export default function BackToTop() {
  const [showButton, setShowButton] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setShowButton(window.scrollY > 300);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  return (
    showButton && (
      <button
        onClick={scrollToTop}
        className="fixed bottom-6 right-6 w-12 h-12 
                   bg-white text-black 
                   dark:bg-gray-800 dark:text-white 
                   text-xl font-bold rounded-sm 
                   flex items-center justify-center 
                   shadow-md hover:bg-gray-200 dark:hover:bg-gray-700 
                   z-50 transition-colors duration-300"
        aria-label="Back to top"
      >
        ^
      </button>
    )
  );
}
