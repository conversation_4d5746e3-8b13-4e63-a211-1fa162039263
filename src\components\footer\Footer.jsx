import "./Footer.css";
import Link from "./Link";
import { links, socalMedia } from "./consts";
import { useTheme } from "../../utils/context/ThemeContext";

function Footer() {
  const year = new Date().getFullYear();
  const { theme } = useTheme();

  return (
    <div className="max-w-screen transition-colors duration-500 bg-[#F0F3FF] dark:bg-[#0e1a34] pt-20 pb-6 px-8">
      <div className="sm:flex justify-between items-center [border-bottom:solid_2px_#adadad] pb-8 text-[#ffffff]">
        <div className="flex gap-12">
          <div className="grid grid-cols-[repeat(2,_1fr)] grid-rows-[repeat(3,_1fr)] gap-y-4 gap-x-8 mb-10 sm:mb-0">
            {links.map((link) => {
              return (
                <Link
                  key={link.link}
                  link={link.link}
                  title={link.title}
                  target=""
                />
              );
            })}
          </div>
        </div>
        <div className="flex items-center justify-center gap-8">
          <p className="dark:text-white text-[#0e1a34] ">
            Catalyze Your Business
          </p>
          <a href="https://blogs.quantumzyme.com/" target="target_blank">
            <img
              src={`/logo_${theme}.png`}
              alt="quantumzyme-logo"
              className="h-12 md:h-14"
            />
          </a>
        </div>
      </div>
      <div className="sm:flex items-center justify-between my-4">
        <p className="dark:text-white text-[#0e1a34] text-sm font-thin dark:opacity-50 mb-6 sm:mb-0">
          Copyrights &copy; {year} All Rights Reserved to Quantumzyme LLP
        </p>
        <div className="text-[x-large]  flex gap-4">
          {socalMedia.map((socalMedia) => {
            return (
              <Link
                key={socalMedia.link}
                link={socalMedia.link}
                title={socalMedia.title}
                target="target_blank"
              />
            );
          })}
        </div>
      </div>
    </div>
  );
}

export default Footer;
