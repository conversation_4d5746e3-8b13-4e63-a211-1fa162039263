import React from "react";
import DOMPurify from "dompurify";
import { sanitizeAndTruncate } from "../../utils/functions/functions";

const SingleBlog = ({ blog }) => {
  // Sanitize HTML for date
  const sanitizeHtml = (dirty) => ({
    __html: DOMPurify.sanitize(dirty),
  });

  return (
    <a
      href={`/singleArticle/${blog.id}`}
      target="_blank"
      rel="noopener noreferrer"
      className="no-underline"
    >
      <div className="flex dark:bg-[#ECF3FF] bg-[#424656] rounded-3xl overflow-hidden">
        <img
          loading="lazy"
          className="w-32 md:w-42 h-auto object-cover rounded-l-3xl"
          src="blog2.png"
          alt={blog.title}
        />
        <div className="flex flex-col p-6 flex-1 min-w-0">
          <h4 className="text-white dark:text-black truncate">{blog.title}</h4>

          <p className="text-white dark:text-black opacity-60 text-sm font-normal my-3 line-clamp-3">
            {sanitizeAndTruncate(blog.para1, 50)}
          </p>

          <span className="text-white dark:text-black text-sm">
            {blog.author_name}
          </span>

          <span
            className="text-white dark:text-black opacity-60 text-sm"
            dangerouslySetInnerHTML={sanitizeHtml(blog.created_on)}
          ></span>
        </div>
      </div>
    </a>
  );
};

export default SingleBlog;
