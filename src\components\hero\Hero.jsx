import { useEffect, useState } from "react";
import "./Hero.css";
import Button from "./Button";

function Hero() {
  const [mount, setMount] = useState(true);
  const [carousel, setCarousel] = useState("image");
  const [timer, setTimer] = useState(5000);

  useEffect(() => {
    setTimeout(() => {
      if (carousel === "image") {
        setCarousel("gif");
        setTimer(19000);
        setMount(false);
      }
      if (carousel === "gif") {
        setCarousel("image");
        setTimer(5000);
        setMount(false);
      }
    }, timer);
  }, [carousel, timer]);

  return (
    <div className="grid relative">
      {carousel === "image" && (
        <>
          <img
            loading="lazy"
            className={`hero hero-image ${
              mount ? "fadeOut" : "fadeInOutImage"
            }`}
            src="/hero_image.jpg"
            alt="hero-image"
          />
          <div className="w-4/5 sm:w-2/3 md:4/5 xl:w-1/5 sm:ml-12 md:mt-14 text-left p-8 self-center [grid-area:1_/_1] z-2">
            <h2 className="text-2xl font-bold">Welcome to Quantumzyme!</h2>
            <p className="sm:text-lg">
              QuantumZyme is a dynamic blog dedicated to exploring the
              fascinating world of competitive biology, where science meets
              strategy and innovation in the biological sciences.
            </p>
          </div>
        </>
      )}
      {carousel === "gif" && (
        <img
          loading="lazy"
          className="hero hero-gif fadeInOutGif"
          src="/hero_video.gif"
          alt="hero-gif"
        />
      )}
      <div className="flex gap-6 absolute bottom-0 left-2/4 -translate-x-1/2 -translate-y-0 mb-4">
        <Button type="image" carousel={carousel} />
        <Button type="gif" carousel={carousel} />
      </div>
    </div>
  );
}

export default Hero;
