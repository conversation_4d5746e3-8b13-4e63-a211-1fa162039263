import useBlogs from "../../utils/api/blogs";
import Library from "../Library/Library";
import LatestBlogs from "../latestBlogs/LatestBlogs";
import PopularBlogs from "../latestBlogs/PopularBlogs";

function Blogs() {
  const { blogs, error, loading } = useBlogs();
  console.log(blogs);
  if (loading) {
    return (
      <div className="transition-colors duration-500 bg-[#F0F3FF] dark:bg-[#0e1a34] dark:text-white text-[#0e1a34] p-10">
        Loading blogs...
      </div>
    );
  }
  if (error || !blogs) {
    return (
      <div className="transition-colors duration-500 bg-[#F0F3FF] dark:bg-[#0e1a34] dark:text-white text-[#0e1a34] p-10">
        Sorry, there was an error in loading blogs
      </div>
    );
  }

  if (!blogs.blogs || blogs?.blogs?.length === 0) {
    return (
      <div className="transition-colors duration-500 bg-[#F0F3FF] dark:bg-[#0e1a34] dark:text-white text-[#0e1a34] p-10">
        No blogs found.
      </div>
    );
  }

  return (
    <>
      <LatestBlogs blogs={blogs} />
      <PopularBlogs blogs={blogs} />
      <Library blogs={blogs} />
    </>
  );
}

export default Blogs;
