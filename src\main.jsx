import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import { BlogProvider } from "./utils/context/cookiesContext.jsx";
import { BrowserRouter, Route, Routes } from "react-router-dom";
import { ThemeProvider } from "./utils/context/ThemeContext.jsx";
import SingleArticle from "./components/singleArticle/SinlgeArticle.jsx";
import App from "./App.jsx";
import "./index.css";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

createRoot(document.getElementById("root")).render(
  <StrictMode>
    <ThemeProvider>
      <BlogProvider>
        <BrowserRouter>
          <ToastContainer />

          <Routes>
            <Route path="/" element={<App />} />
            <Route path="/blogs/:id" element={<SingleArticle />} />
          </Routes>
        </BrowserRouter>
      </BlogProvider>
    </ThemeProvider>
  </StrictMode>
);
