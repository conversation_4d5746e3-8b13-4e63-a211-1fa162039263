import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import { BlogProvider } from "./utils/context/cookiesContext.jsx";
import { BrowserRouter, Route, Routes } from "react-router-dom";
import { ThemeProvider } from "./utils/context/ThemeContext.jsx";
import App from "./App.jsx";
import "./index.css";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import SinlgeArticle from "./components/singleArticle/SinlgeArticle.jsx";

createRoot(document.getElementById("root")).render(
  <StrictMode>
    <ThemeProvider>
      <BlogProvider>
        <BrowserRouter>
          <ToastContainer />

          <Routes>
            <Route path="/" element={<App />} />
            <Route path="/singleArticle/:id" element={<SinlgeArticle />} />
          </Routes>
        </BrowserRouter>
      </BlogProvider>
    </ThemeProvider>
  </StrictMode>
);
