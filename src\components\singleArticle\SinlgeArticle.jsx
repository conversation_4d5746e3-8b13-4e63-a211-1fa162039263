import useSingleBlog from "../../utils/api/singleBlog";
import Article from "./Article/Article";
import RelatedArticles from "./RelatedArticles/RelatedArticles";
import BackToTop from "./BackToTop/BackToTop";
import Body from "./Body/Body";
import Header from "../Header/Header";
import Footer from "../footer/Footer";
import { useParams } from "react-router";

function SinlgeArticle() {
  const { id } = useParams();
  const { blog: singleArticle, error, loading } = useSingleBlog(id);

  if (loading)
    return (
      <div className="flex justify-center items-center h-[100vh] bg-white text-black dark:bg-gray-900 dark:text-white ">
        <Header />
        Article is loading...
      </div>
    );

  if (error || !singleArticle)
    return (
      <div className="flex justify-center items-center h-[100vh] bg-white text-black dark:bg-gray-900 dark:text-white ">
        <Header />
        {!singleArticle && !error
          ? "Article not found"
          : "Sorry, there was an error loading the article"}
      </div>
    );

  return (
    <div className="w-full overflow-x-hidden min-h-screen bg-white text-black dark:bg-gray-900 dark:text-white transition-colors duration-500">
      <Header />
      <main className="px-8 py-10">
        <Article article={singleArticle} />
        <Body article={singleArticle} />
        <RelatedArticles />
        <BackToTop />
        <Footer />
      </main>
    </div>
  );
}

export default SinlgeArticle;
