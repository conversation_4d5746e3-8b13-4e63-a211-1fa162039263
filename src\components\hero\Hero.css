.hero {
  opacity: 0;
  animation-duration: 2s;
  animation-timing-function: ease-in-out;
  animation-fill-mode: forwards;
}

.hero-image {
  width: 100%;
  height: 60vh;
  object-fit: cover;
  grid-area: 1 / 1;
}

.hero-gif {
  width: 100%;
  height: 60vh;
  object-fit: cover;
}

.fadeOut {
  animation-name: fadeOutMounth;
  animation-duration: 5s;
}

.fadeInOutImage {
  animation-name: fadeInOut;
  animation-duration: 5s;
}

.fadeInOutGif {
  animation-name: fadeInOut;
  animation-duration: 19s;
}

@keyframes fadeInOut {
  0% {
    opacity: 0.5;
  }
  20% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    opacity: 0.5;
  }
}
@keyframes fadeOutMounth {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 1;
  }
  80% {
    opacity: 1;
  }
  100% {
    opacity: 0.5;
  }
}

label input[type="checkbox"] {
  position: absolute;
  left: 50px;
  visibility: hidden;
}
