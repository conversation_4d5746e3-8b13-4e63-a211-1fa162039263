import { useState } from "react";
import Blog from "./Blog";
import LibraryNav from "./LibraryNav";
import LoadMore from "./LoadMore";
import SavedBlogs from "./SavedBlogs";

function Library({ blogs }) {
  const [visibleCount, setVisibleCount] = useState(4);
  const [searchTerm, setSearchTerm] = useState("");
  const [sort, setSort] = useState("allBlogs");

  const filteredBlogs = blogs?.blogs?.filter((blog) =>
    blog.title.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="transition-colors duration-500 bg-[#F0F3FF] dark:bg-[#0e1a34] text-white px-8">
      <h2 className="dark:text-white text-[#0e1a34] font-bold text-3xl opacity-85">
        Library
      </h2>
      <LibraryNav
        sort={sort}
        setSort={setSort}
        searchTerm={searchTerm}
        setSearchTerm={setSearchTerm}
      />
      <div className="sm:px-20 flex flex-col gap-6">
        {filteredBlogs?.length === 0 && (
          <div className="transition-colors duration-500 bg-[#F0F3FF] dark:bg-[#0e1a34] dark:text-white text-[#0e1a34] sm:p-10">
            No blogs found.
          </div>
        )}
        {sort === "allBlogs" &&
          filteredBlogs
            ?.slice(0, visibleCount)
            .map((blog, index) => <Blog key={index} blog={blog} />)}
        {sort === "saved" && <SavedBlogs blogs={blogs} />}
        {sort === "alphabetically" &&
          filteredBlogs
            ?.sort((a, b) => a.title.localeCompare(b.title))
            .slice(0, visibleCount)
            .map((blog, index) => <Blog key={index} blog={blog} />)}

        {sort === "date" &&
          filteredBlogs
            ?.sort((a, b) => {
              const dateA = new Date(
                a.created_on.match(/datetime="([^"]+)"/)?.[1] ||
                  a.created_on ||
                  0
              );
              const dateB = new Date(
                b.created_on.match(/datetime="([^"]+)"/)?.[1] ||
                  b.created_on ||
                  0
              );
              return dateB - dateA;
            })
            .slice(0, visibleCount)
            .map((blog, index) => <Blog key={index} blog={blog} />)}
        {sort !== "saved" && (
          <LoadMore
            visibleCount={visibleCount}
            blogs={blogs}
            filteredBlogs={filteredBlogs}
            setVisibleCount={setVisibleCount}
          />
        )}
      </div>
    </div>
  );
}

export default Library;
