import { useState, useRef } from "react";
import { Link } from "react-router";
import { useTheme } from "../../utils/context/ThemeContext";

function Header() {
  const [openDropdown, setOpenDropdown] = useState(null);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const timeoutRef = useRef(null);
  const { theme, toggleTheme } = useTheme();

  const handleMouseEnter = (menu) => {
    clearTimeout(timeoutRef.current);
    setOpenDropdown(menu);
  };

  const handleMouseLeave = () => {
    timeoutRef.current = setTimeout(() => {
      setOpenDropdown(null);
    }, 300);
  };

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
    setOpenDropdown(null);
  };

  return (
    <nav className="fixed top-0 left-0 w-full z-50 bg-[#F0F3FF] dark:bg-gray-900 text-black dark:text-white px-4 md:px-8 py-4 md:py-6 flex items-center justify-between shadow transition-colors duration-300">
      <div className="flex items-center justify-between w-full md:w-auto">
        <a
          href="https://www.quantumzyme.com/"
          target="_blank"
          rel="noopener noreferrer"
        >
          <img
            src={`/logo_${theme}.png`}
            alt="Quantumzyme Logo"
            className="h-12 md:h-14"
          />
        </a>
        <button
          onClick={toggleMobileMenu}
          className="md:hidden ml-auto text-black dark:text-white focus:outline-none"
          aria-label="Toggle menu"
        >
          <img
            src={`/logo_dark.png`}
            alt="Quantumzyme Logo"
            className="h-full w-auto max-h-14 object-contain"
          />
        </button>

        {/* Hamburger Button for Mobile */}
        <button
          onClick={toggleMobileMenu}
          className="md:hidden ml-auto text-black dark:text-white focus:outline-none"
        >
          <svg
            className="w-6 h-6"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            {mobileMenuOpen ? (
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            ) : (
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 6h16M4 12h16M4 18h16"
              />
            )}
          </svg>
        </button>
      </div>

      <ul
        className={`${
          mobileMenuOpen ? "flex" : "hidden md:flex"
        } flex-col md:flex-row md:space-x-10 space-y-4 md:space-y-0 font-medium items-center md:items-center absolute md:static top-full left-0 w-full md:w-auto bg-[#F0F3FF] dark:bg-gray-900 px-4 md:px-0 py-4 md:py-0 shadow-md md:shadow-none transition-all duration-300 ease-in-out`}
      >
        {[
          {
            name: "About Us ▾",
            key: "about",
            url: "https://www.quantumzyme.com/about",
            subLinks: [
              ["Vision", "https://quantumzyme.com/about#mission_vision"],
              ["Leadership", "https://quantumzyme.com/about#qz_team"],
              [
                "Scientific Advisory Board",
                "https://quantumzyme.com/about#leaders",
              ],
              ["Our Journey", "https://quantumzyme.com/about#qz_journey"],
            ],
          },
          {
            name: "Technology",
            key: null,
            url: "https://www.quantumzyme.com/technology",
          },
          {
            name: "Services ▾",
            key: "services",
            url: "https://www.quantumzyme.com/services",
            subLinks: [
              [
                "Enzyme Discovery",
                "https://quantumzyme.com/services#enzyme_discovery",
              ],
              [
                "Enzyme Engineering",
                "https://quantumzyme.com/services#serv_enzyme_engineering",
              ],
              [
                "Wet Lab Validation",
                "https://quantumzyme.com/services#serv_wetlab_validation",
              ],
              [
                "Process Development",
                "https://quantumzyme.com/services#serv_process_development",
              ],
              ["Scale-Up", "https://quantumzyme.com/services#serv_scaleup"],
            ],
          },
          {
            name: "R & D ▾",
            key: "rnd",
            url: "https://www.quantumzyme.com/research_and_development",
            subLinks: [
              [
                "R&D Pipeline",
                "https://quantumzyme.com/research_and_development#v-pills-profile-tab",
              ],
              [
                "Publications",
                "https://quantumzyme.com/research_and_development#pills-pub-tab",
              ],
              [
                "Case Studies",
                "https://quantumzyme.com/research_and_development#pills-case-tab",
              ],
            ],
          },
          {
            name: "Media ▾",
            key: "media",
            url: "https://www.quantumzyme.com/media",
            subLinks: [
              [
                "In the News",
                "https://quantumzyme.com/media#pills-press-releases-tab",
              ],
              [
                "Company Statements",
                "https://quantumzyme.com/media#pills-company-statements-tab",
              ],
              ["Events", "https://quantumzyme.com/media#pills-mevents-tab"],
            ],
          },
          {
            name: "Careers",
            key: null,
            url: "https://www.quantumzyme.com/careers",
          },
        ].map(({ name, key, url, subLinks }) => (
          <li
            key={name}
            className="relative cursor-pointer hover:text-blue-500 dark:hover:text-blue-500 w-auto"
            onMouseEnter={() => key && handleMouseEnter(key)}
            onMouseLeave={() => key && handleMouseLeave()}
          >
            <Link to={url}>{name}</Link>
            {openDropdown === key && subLinks && (
              <ul className="md:absolute md:top-full md:left-0 w-full md:w-56 bg-[#F0F3FF] dark:bg-gray-800 text-black dark:text-white mt-2 py-2 px-4 rounded shadow z-50">
                {subLinks.map(([title, link]) => (
                  <li
                    key={title}
                    className="py-1 hover:text-blue-500 dark:hover:text-blue-500 rounded"
                  >
                    <Link to={link}>{title}</Link>
                  </li>
                ))}
              </ul>
            )}
          </li>
        ))}

        <li className="w-auto">
          <button
            onClick={toggleTheme}
            className="relative w-16 h-8 bg-indigo-200 dark:bg-slate-600 rounded-full flex items-center px-1 transition-colors duration-300"
            aria-label="Toggle theme"
          >
            <div
              className={`w-6 h-6 bg-white rounded-full flex items-center justify-center shadow-md transform transition-transform duration-300 ${
                theme === "dark" ? "translate-x-8" : "translate-x-0"
              }`}
            >
              <span className="text-black">
                {theme === "dark" ? "🌙" : "☀️"}
              </span>
            </div>
          </button>
        </li>
      </ul>
    </nav>
  );
}

export default Header;
