import { useState } from "react";
import SingleBlog from "./SingleBlog";
import DOMPurify from "dompurify";
import { Search } from "lucide-react";
import { sanitizeAndTruncate } from "../../utils/functions/functions";

const LatestBlogs = ({ blogs }) => {
  const [searchTerm, setSearchTerm] = useState("");

  const filteredBlogs = blogs?.blogs?.filter((blog) =>
    blog.title.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const firstBlog = filteredBlogs[0];

  return (
    <div className="py-20 px-8 transition-colors duration-500 bg-[#F0F3FF] dark:bg-[#0e1a34]">
      {/* Header with search */}
      <div className="flex flex-wrap justify-between items-center mb-8">
        <h1 className="dark:text-white text-[#0e1a34] font-bold text-3xl opacity-85 mb-6 sm:mb-0">
          Latest Blogs
        </h1>
        <div className="relative">
          <input
            type="text"
            placeholder="Search..."
            className="bg-transparent border dark:border-white border-[#0e1a34] rounded-full px-4 py-1 pr-10 dark:text-white text-[#0e1a34]  text-sm focus:outline-none"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 dark:text-white text-[#0e1a34] w-4 h-4" />
        </div>
      </div>

      <div className="flex flex-wrap lg:flex-nowrap gap-8">
        {/* Featured blog */}
        {firstBlog && (
          <a
            href={`/singleArticle/${firstBlog.id}`}
            target="_blank"
            rel="noopener noreferrer"
            className="no-underline"
          >
            <div className="flex flex-col dark:bg-[#ECF3FF] bg-[#424656] rounded-3xl">
              <img
                loading="lazy"
                className="h-48 w-full object-cover rounded-t-3xl"
                src="blog1.png"
                alt="firstblog"
              />
              <div className="flex flex-col p-6">
                <h4 className="text-white dark:text-black  text-lg font-semibold">
                  {firstBlog.title}
                </h4>
                <p className="text-white dark:text-black opacity-60 !text-sm font-normal my-3">
                  {sanitizeAndTruncate(firstBlog.para1, 30)}
                </p>
                <span className="text-white dark:text-black k text-sm">
                  By {firstBlog.author_name}
                </span>
                <span
                  className="text-white dark:text-black opacity-60 !text-sm"
                  dangerouslySetInnerHTML={{
                    __html: DOMPurify.sanitize(firstBlog.created_on),
                  }}
                ></span>
              </div>
            </div>
          </a>
        )}

        {/* Rest of blogs */}
        <div className="flex flex-col gap-3 overflow-y-scroll max-h-[448px]">
          {filteredBlogs.slice(1).map((blog, id) => (
            <SingleBlog key={id} blog={blog} />
          ))}
        </div>
      </div>
    </div>
  );
};

export default LatestBlogs;
