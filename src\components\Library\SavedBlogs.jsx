import { useBlogContext } from "../../utils/context/cookiesContext";
import { sanitizeAndTruncate } from "../../utils/functions/functions";
import DOMPurify from "dompurify";

function SavedBlogs({ blogs }) {
  const { getSavedIds } = useBlogContext();
  const savedIds = getSavedIds();

  const savedBlogs = blogs?.blogs?.filter((blog) =>
    savedIds.includes(blog.id.toString())
  );

  if (savedBlogs.length === 0 || savedBlogs === null)
    return (
      <div className="transition-colors duration-500 bg-[#F0F3FF] dark:bg-[#0e1a34] dark:text-white text-[#0e1a34] p-10">
        There is no saved yet.
      </div>
    );

  if (!savedBlogs || savedBlogs === undefined)
    return (
      <div className="transition-colors duration-500 bg-[#F0F3FF] dark:bg-[#0e1a34] dark:text-white text-[#0e1a34] p-10">
        There was an error getting saved blogs.
      </div>
    );

  return (
    <>
      {savedBlogs.map((blog, i) => (
        <div
          key={i}
          className="dark:bg-[#D9D9D933] bg-[#D9D9D9] dark:text-white text-[#0E1A34] hover:bg-[#538FF833] rounded-xl shadow-md "
        >
          <div className="xl:flex gap-4 ">
            <div className="flex-1 p-8">
              <h2 className="text-lg font-semibold mb-2">{blog.title}</h2>
              <div className="flex gap-10 justify-center items-center">
                <p className="text-sm dark:text-white text-[#0E1A34] dark:opacity-70 opacity-50 font-normal mb-5">
                  {sanitizeAndTruncate(blog.field_paragraph1, 90)}
                </p>
              </div>
              <p className="text-lg dark:text-white text-[#0E1A34]">
                {blog.field_author_name}
              </p>
              <p
                className="dark:text-white text-[#0E1A34] opacity-40 !text-sm"
                dangerouslySetInnerHTML={{
                  __html: DOMPurify.sanitize(blog.field_date),
                }}
              ></p>
            </div>
            <img src="blog1.png" alt="Thumbnail" className="" />
          </div>
        </div>
      ))}
    </>
  );
}

export default SavedBlogs;
