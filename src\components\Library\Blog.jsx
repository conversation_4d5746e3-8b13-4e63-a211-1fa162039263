import { useState } from "react";
import { useBlogContext } from "../../utils/context/cookiesContext";
import { sanitizeAndTruncate } from "../../utils/functions/functions";
import DOMPurify from "dompurify";

function SaveButton({ onClick }) {
  return (
    <svg
      width="21"
      height="21"
      viewBox="0 0 21 21"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      onClick={onClick}
    >
      <g clipPath="url(#clip0_265_1012)">
        <path
          d="M17.3226 20.5C17.0143 20.4992 16.7093 20.4372 16.4251 20.3178C16.1408 20.1984 15.8831 20.0238 15.6668 19.8042L10.5418 14.7092L5.41677 19.8075C5.08763 20.1414 4.66551 20.3685 4.20545 20.459C3.74539 20.5495 3.26873 20.4993 2.8376 20.315C2.4022 20.1399 2.02972 19.8375 1.76882 19.4474C1.50791 19.0573 1.3707 18.5976 1.3751 18.1283V4.66667C1.3751 3.5616 1.81409 2.50179 2.59549 1.72039C3.37689 0.938987 4.4367 0.5 5.54177 0.5L15.5418 0.5C16.0889 0.5 16.6308 0.607774 17.1363 0.817169C17.6418 1.02656 18.1011 1.33348 18.488 1.72039C18.875 2.1073 19.1819 2.56663 19.3913 3.07215C19.6007 3.57768 19.7084 4.11949 19.7084 4.66667V18.1283C19.7131 18.5972 19.5764 19.0567 19.3161 19.4467C19.0558 19.8368 18.6841 20.1393 18.2493 20.315C17.9558 20.4377 17.6407 20.5006 17.3226 20.5ZM5.54177 2.16667C4.87873 2.16667 4.24284 2.43006 3.774 2.8989C3.30516 3.36774 3.04177 4.00363 3.04177 4.66667V18.1283C3.04147 18.2672 3.08233 18.403 3.15919 18.5187C3.23605 18.6343 3.34545 18.7246 3.4736 18.7781C3.60174 18.8316 3.74286 18.8459 3.87914 18.8193C4.01542 18.7926 4.14074 18.7262 4.23927 18.6283L9.95844 12.9442C10.1146 12.789 10.3258 12.7018 10.5459 12.7018C10.7661 12.7018 10.9773 12.789 11.1334 12.9442L16.8459 18.6267C16.9445 18.7245 17.0698 18.791 17.2061 18.8176C17.3423 18.8443 17.4835 18.83 17.6116 18.7765C17.7398 18.7229 17.8492 18.6327 17.926 18.517C18.0029 18.4014 18.0437 18.2655 18.0434 18.1267V4.66667C18.0434 4.00363 17.78 3.36774 17.3112 2.8989C16.8424 2.43006 16.2065 2.16667 15.5434 2.16667H5.54177Z"
          fill="white"
          className="dark:fill-white fill-[#0E1A34]"
        />
      </g>
      <defs>
        <clipPath id="clip0_265_1012">
          <rect
            width="20"
            height="20"
            fill="white"
            transform="translate(0.541748 0.5)"
          />
        </clipPath>
      </defs>
    </svg>
  );
}

function LinkButton({ onClick }) {
  return (
    <svg
      width="28"
      height="26"
      viewBox="0 0 28 26"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      onClick={onClick}
      className="cursor-pointer"
    >
      <path
        d="M18.3297 10.7894C18.524 10.6101 18.7825 10.5023 19.0568 10.4864C19.331 10.4705 19.6021 10.5475 19.8193 10.703L19.9253 10.7894L22.3188 12.9999C23.4643 14.0584 24.1158 15.4886 24.1339 16.9848C24.152 18.4809 23.5353 19.9242 22.4157 21.0061C21.2961 22.088 19.7624 22.7227 18.1425 22.7745C16.5225 22.8263 14.945 22.291 13.7469 21.2832L13.5416 21.1019L11.1481 18.8926C10.9435 18.7055 10.8242 18.4534 10.8144 18.1878C10.8046 17.9222 10.9052 17.6632 11.0956 17.4638C11.2859 17.2644 11.5516 17.1397 11.8383 17.1153C12.125 17.0908 12.4109 17.1684 12.6377 17.3321L12.7437 17.4186L15.1372 19.629C15.8636 20.2999 16.8447 20.683 17.8722 20.6971C18.8996 20.7111 19.8925 20.355 20.6402 19.7043C21.3878 19.0536 21.8313 18.1596 21.8765 17.212C21.9217 16.2644 21.5651 15.3378 20.8823 14.629L20.7232 14.4728L18.3297 12.2634C18.2247 12.1667 18.1415 12.0518 18.0847 11.9253C18.0279 11.7989 17.9987 11.6633 17.9987 11.5264C17.9987 11.3895 18.0279 11.254 18.0847 11.1275C18.1415 11.0011 18.2247 10.8862 18.3297 10.7894ZM10.348 10.053C10.5423 9.87363 10.8008 9.76589 11.0751 9.74997C11.3493 9.73405 11.6204 9.81105 11.8376 9.96652L11.9448 10.053L16.7318 14.4728C16.9341 14.6602 17.0516 14.9118 17.0604 15.1763C17.0691 15.4408 16.9685 15.6985 16.7789 15.897C16.5892 16.0955 16.3249 16.22 16.0395 16.2451C15.754 16.2702 15.469 16.194 15.2422 16.0321L15.1361 15.9457L10.348 11.5269C10.2431 11.4302 10.1598 11.3153 10.103 11.1889C10.0463 11.0624 10.017 10.9269 10.017 10.79C10.017 10.6531 10.0463 10.5175 10.103 10.3911C10.1598 10.2646 10.2431 10.1497 10.348 10.053ZM4.76205 4.89673C5.89142 3.85426 7.41268 3.2538 9.00941 3.22024C10.6061 3.18667 12.1554 3.72259 13.3351 4.71652L13.5404 4.89777L15.9339 7.10611C16.1385 7.29313 16.2578 7.54524 16.2676 7.81084C16.2774 8.07644 16.1768 8.33545 15.9864 8.53483C15.7961 8.73422 15.5304 8.85892 15.2437 8.8834C14.957 8.90788 14.6711 8.8303 14.4443 8.66652L14.3383 8.58006L11.9448 6.37069C11.2183 5.69983 10.2373 5.31672 9.20984 5.30266C8.1824 5.2886 7.18947 5.64471 6.44182 6.2954C5.69417 6.94609 5.25068 7.84012 5.20548 8.78771C5.16028 9.73531 5.51693 10.6619 6.19972 11.3707L6.35884 11.5269L8.75233 13.7353C8.95687 13.9223 9.07623 14.1744 9.086 14.44C9.09576 14.7056 8.99518 14.9646 8.80484 15.164C8.6145 15.3634 8.3488 15.4881 8.06213 15.5126C7.77545 15.537 7.48948 15.4595 7.26274 15.2957L7.15667 15.2092L4.76431 12.9999C3.6004 11.9254 2.94653 10.4682 2.94653 8.94881C2.94653 7.42939 3.6004 5.97219 4.76431 4.89777L4.76205 4.89673Z"
        fill="white"
        className="dark:fill-white fill-[#0E1A34] cursor-pointer"
      />
    </svg>
  );
}

function Blog({ blog }) {
  const { saveBlogId } = useBlogContext();
  const [showModal, setShowModal] = useState(false);
  const [screenSize] = useState(() => {
    return window.innerWidth >= 700 ? true : false;
  });

  const handleCopyLink = (e) => {
    e.stopPropagation();
    navigator.clipboard
      .writeText(`http://localhost:5173/singleArticle/${blog.id}`)
      .then(() => {
        setShowModal(true);
        setTimeout(() => setShowModal(false), 2000);
      });
  };

  return (
    <div
      onClick={() => {
        window.open(`/singleArticle/${blog.id}`, "_blank");
      }}
      className="dark:bg-[#D9D9D933] bg-[#D9D9D9] dark:text-white text-[#0E1A34] hover:bg-[#538FF833] rounded-xl shadow-md "
    >
      <div className="xl:flex gap-4 ">
        <div className="flex-1 sm:p-8 p-2">
          <h2 className="text-lg font-semibold mb-2">{blog.title}</h2>
          <div className="flex sm:gap-10 justify-center items-center">
            <p className="text-xs sm:text-sm dark:text-white text-[#0E1A34] dark:opacity-70 opacity-50 font-normal mb-5">
              {sanitizeAndTruncate(blog.para1, screenSize ? 90 : 30)}
            </p>
            <div className="flex flex-col self-start gap-6 relative">
              <SaveButton onClick={() => saveBlogId(blog.id)} />
              <LinkButton onClick={handleCopyLink} />
              {showModal && (
                <div className="absolute bottom-0 mt-1 bg-white text-black px-3 py-1 text-xs rounded shadow-md z-10 whitespace-nowrap">
                  Link copied!
                </div>
              )}
            </div>
          </div>
          <p className="text-lg dark:text-white text-[#0E1A34]">
            {blog.author_name}
          </p>
          <p
            className="dark:text-white text-[#0E1A34] opacity-40 !text-sm"
            dangerouslySetInnerHTML={{
              __html: DOMPurify.sanitize(blog.created_on),
            }}
          ></p>
        </div>
        <img loading="lazy" src="blog1.png" alt="Thumbnail" className="" />
      </div>
    </div>
  );
}

export default Blog;
