import { createContext, useContext } from "react";
import { toast } from "react-toastify";
const BlogContext = createContext();

export const BlogProvider = ({ children }) => {

  
  const saveBlogId = (id) => {
  try {
    const existing = getSavedIds();
    if (!existing.includes(id)) {
      existing.push(id);
      document.cookie = `savedBlogIds=${encodeURIComponent(
        JSON.stringify(existing)
      )}; path=/; max-age=604800; samesite=Lax`;
      return { success: true };
    } else {
      return { success: false, message: "Already saved." };
    }
  } catch (error) {
    return { success: false, message: "An error occurred while saving." };
  }
};


  const getSavedIds = () => {
    const cookie = document.cookie
      .split("; ")
      .find((row) => row.startsWith("savedBlogIds="));
    return cookie ? JSON.parse(decodeURIComponent(cookie.split("=")[1])) : [];
  };

  return (
    <BlogContext.Provider
      value={{
        saveBlogId,
        getSavedIds,
      }}
    >
      {children}
    </BlogContext.Provider>
  );
};

// eslint-disable-next-line react-refresh/only-export-components
export const useBlogContext = () => useContext(BlogContext);
