import { useState } from "react";
import DOMPurify from "dompurify";
import { ChevronsRight } from "lucide-react";
import { sanitizeAndTruncate } from "../../utils/functions/functions";

const PopularBlogs = ({ blogs }) => {
  const blogsData = blogs?.blogs || [];

  const [startIndex, setStartIndex] = useState(0);

  const visibleBlogs = [];
  for (let i = 0; i < 3; i++) {
    visibleBlogs.push(blogsData[(startIndex + i) % blogsData.length]);
  }

  const handleNext = () => {
    setStartIndex((prevIndex) => (prevIndex + 3) % blogsData.length);
  };

  return (
    <div className="py-20 px-8 transition-colors duration-500 bg-[#F0F3FF] dark:bg-[#0e1a34]">
      <div className="mb-8 flex justify-between items-center">
        <h1 className="dark:text-white text-[#0e1a34] font-bold text-3xl opacity-85">
          Popular Blog
        </h1>
      </div>

      <div className="flex gap-6 flex-wrap justify-between items-stretch px-12">
        {visibleBlogs.map((blog, id) => (
          <div
            onClick={() => {
              window.open(`/singleArticle/${blog.id}`, "_blank");
            }}
            key={id}
            className="w-full sm:w-[48%] lg:w-[32%] flex flex-col rounded-xl overflow-hidden shadow-md bg-white dark:bg-[#1e293b] h-[400px]"
          >
            <img
              loading="lazy"
              src="blog1.png"
              alt={blog.title}
              className="w-full h-48 object-cover"
            />

            <div className="p-4 flex flex-col flex-grow relative">
              <h2 className="text-lg font-semibold text-[#0e1a34] dark:text-white mb-2 line-clamp-2 hover:underline">
                {blog.title}
              </h2>

              <p className="text-sm text-gray-600 dark:text-gray-300 mb-3 line-clamp-3">
                {sanitizeAndTruncate(blog.para1, 30)}
              </p>

              <div className="flex items-center justify-between mt-auto">
                <div className="flex flex-col">
                  <span className="text-sm font-medium text-gray-800 dark:text-gray-200">
                    By {blog.author_name}
                  </span>
                  <span
                    className="text-sm text-gray-500 dark:text-gray-400"
                    dangerouslySetInnerHTML={{
                      __html: DOMPurify.sanitize(blog.created_on),
                    }}
                  ></span>
                </div>

                <div
                  onClick={handleNext}
                  className="cursor-pointer hover:scale-110 transition-transform select-none"
                  title="Next Blogs"
                >
                  <ChevronsRight className="w-5 h-5 text-[#0e1a34] dark:text-white" />
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default PopularBlogs;
