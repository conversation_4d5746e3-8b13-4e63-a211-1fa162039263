import { useState, useEffect } from "react";
import axios from "axios";

const useSingleBlog = (blogId) => {
  const [blog, setBlog] = useState(null);
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchBlog = () => {
      if (!blogId) {
        setLoading(false);
        return;
      }

      axios
        .get(
          `https://digitalnexusai.com/qzblogsapi/getBlogbyId.php?blogid=${blogId}`
        )
        .then((res) => {
          const blogData = res.data?.Blog?.[0];
          setBlog(blogData);
        })
        .catch((err) => {
          setError(err);
        })
        .finally(() => {
          setLoading(false);
        });
    };

    fetchBlog();
  }, [blogId]);

  return { blog, error, loading };
};

export default useSingleBlog;
