import { useState, useEffect } from "react";
import axios from "axios";

const useBlogs = () => {
  const [blogs, setBlogs] = useState(null);
  const [error, setError] = useState("");
  const [loading, setloading] = useState(true);

  const fetchData = () => {
    axios
      .get("https://digitalnexusai.com/qzblogsapi/getBlogs.php")
      .then((res) => {
        // The new API returns {count, blogs} directly in res.data
        setBlogs(res.data);
      })
      .catch((err) => {
        setError(err);
      })
      .finally(() => {
        setloading(false);
      });
  };

  useEffect(() => {
    fetchData();
  }, []);

  return { blogs, error, loading };
};

export default useBlogs;
