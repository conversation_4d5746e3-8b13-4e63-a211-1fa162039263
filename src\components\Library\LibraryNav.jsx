import { useRef, useState } from "react";
import { FaAngleDown } from "react-icons/fa";

function LibraryNav({ sort, setSort, searchTerm, setSearchTerm }) {
  const [openDropdown, setOpenDropdown] = useState(null);

  const timeoutRef = useRef(null);

  const handleMouseEnter = (menu) => {
    clearTimeout(timeoutRef.current);
    setOpenDropdown(menu);
  };

  const handleMouseLeave = () => {
    timeoutRef.current = setTimeout(() => {
      setOpenDropdown(null);
    }, 300);
  };

  return (
    <div className="flex flex-col sm:flex-row items-center justify-between sm:py-12 py-4 sm:px-8 opacity-90">
      <div className="flex md:flex-row gap-6 mb-4 sm:mb-0 dark:text-white text-[#0e1a34] justify-center items-center">
        <button
          onClick={() => setSort("allBlogs")}
          className={`${sort === "allBlogs" && "underline underline-offset-8"}`}
        >
          All blogs
        </button>
        <button
          onClick={() => setSort("saved")}
          className={`${sort === "saved" && "underline underline-offset-8"}`}
        >
          Saved
        </button>
        <button
          className={`relative flex items-center-safe gap-1 ${
            sort === "alphabetically" || sort === "date"
              ? "underline underline-offset-8"
              : ""
          }`}
          onMouseEnter={() => handleMouseEnter("sortBy")}
          onMouseLeave={handleMouseLeave}
        >
          Sort by <FaAngleDown />
          {openDropdown === "sortBy" && (
            <>
              <ul className="absolute top-full left-0  bg-[#173b58] mt-2 py-2 px-4 rounded shadow z-50">
                <li
                  onClick={() => setSort("alphabetically")}
                  className="py-1 text-white hover:[transition:0.7s] hover:text-[#538ff8] rounded"
                >
                  Alphabetically
                </li>
                <li
                  onClick={() => setSort("date")}
                  className="py-1 text-white hover:[transition:0.7s] hover:text-[#538ff8] rounded"
                >
                  Date
                </li>
              </ul>
            </>
          )}
        </button>
      </div>
      <div className="sm:flex gap-6">
        <p className="dark:text-white text-[#0e1a34]">Search by keywords:</p>
        <input
          type="text"
          className="bg-gray-600 py-1 w-32 md:w-64 pl-2 border h-8 dark:border-white border-[#0e1a34] rounded-full  text-white text-sm focus:outline-none"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </div>
    </div>
  );
}

export default LibraryNav;
