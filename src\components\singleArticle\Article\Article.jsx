import { useBlogContext } from "../../../utils/context/cookiesContext";
import { sanitizeHtml } from "../../../utils/functions/functions";
import { toast } from "react-toastify";
function Article({ article }) {
  const { saveBlogId } = useBlogContext();
  
  return (
    <div className="font-sans text-gray-800 dark:text-white bg-gray-100 dark:bg-gray-900">
      <div className="relative w-screen left-1/2 right-1/2 -ml-[50vw] -mr-[50vw]">
        <img
          src="/blog1.png"
          alt="Article Cover"
          className="w-full h-[200px] sm:h-[300px] md:h-[400px] lg:h-[544px] object-cover m-0 p-0"
        />

        <div className="absolute top-0 left-0 w-full h-full flex flex-col justify-center items-start px-4 sm:px-6 md:px-16 bg-black/40">
          <h1 className="text-lg sm:text-xl md:text-3xl lg:text-4xl font-bold text-white max-w-3xl leading-snug md:leading-tight">
            {article.title}
          </h1>
          <p className="mt-3 text-xs sm:text-sm md:text-base text-gray-200">
            <span> {sanitizeHtml(article.field_date)}</span> <br />{" "}
            <span> {article.field_author_name}</span>
          </p>
          <button
  onClick={() => {
    const result = saveBlogId(article.field_id);
    console.log(result)

    if (result.success) {
      toast.success("Blog successfully saved!", {
        position: "top-right",
        autoClose: 3000,
      });
    } else {
      toast.error(result.message || "Failed to save blog.", {
        position: "top-right",
        autoClose: 3000,
      });
    }
  }}
  className="bg-gray-600 text-white 
             px-4 py-2 
             sm:px-5 sm:py-2.5 
             md:px-6 md:py-3 
             rounded 
             hover:bg-gray-700 
             transition duration-200 
             cursor-pointer 
             my-3 text-sm sm:text-base md:text-lg"
>
  Save
</button>




        </div>
      </div>
    </div>
  );
}

export default Article;
