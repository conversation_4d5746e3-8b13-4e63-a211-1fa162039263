import { CiSquareChevUp } from "react-icons/ci";

function ButtonUp() {
  return (
    <button
      onClick={() =>
        window.scrollTo({
          top: 0,
          left: 0,
          behavior: "smooth",
        })
      }
      className="text-7xl self-end mt-12 dark:text-white text-[#0e1a34] cursor-pointer"
    >
      <CiSquareChevUp />
    </button>
  );
}
function LoadMore({ visibleCount, blogs, filteredBlogs, setVisibleCount }) {
  const handleLoadMore = () => {
    setVisibleCount((prevCount) => prevCount + 4);
  };

  return (
    <>
      {visibleCount < (blogs?.blogs?.length || 0) ? (
        <div className="flex justify-between items-center">
          <div></div>
          {filteredBlogs.length < 4 ? (
            ""
          ) : (
            <button
              onClick={handleLoadMore}
              className="px-6 py-2 rounded-4xl bg-[#538FF8] text-white  hover:bg-blue-700 mt-12"
            >
              Load More
            </button>
          )}
          <ButtonUp />
        </div>
      ) : (
        <ButtonUp />
      )}
    </>
  );
}

export default LoadMore;
