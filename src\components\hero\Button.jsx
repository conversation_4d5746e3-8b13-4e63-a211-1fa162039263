import "./Hero.css";

function Button({ type, carousel }) {
  return (
    <button>
      <div>
        <label
          className="block w-[16px] h-[16px] rounded-[100%] relative overflow-hidden [box-shadow:0px_0px_0px_2px_#0e1a34] "
          style={{ backgroundColor: carousel === type && "#0e1a34" }}
          aria-label="chechBox"
        >
          <input id="ch1" type="checkbox" />
          <div className="transition"></div>
        </label>
      </div>
    </button>
  );
}

export default Button;
