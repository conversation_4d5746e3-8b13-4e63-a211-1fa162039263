import React, { useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, ArrowRight } from "lucide-react";

const blogs = [
  {
    id: 1,
    image: "/images/blog1.png",
    title:
      "Navigation the Inclusion Body Challenge: Problems and Effective Strategies",
    paragraph:
      "Inclusion bodies (IB) are dense, aggregated, spherical and intracellular proteins found in the prokaryotic cytoplasm or periplasm ...",
    tags: [
      "Inclusion Bodies",
      "Protein Aggregation",
      "Cell Lysis",
      "Enzymatic Disrpution",
      "Fusion Tags",
    ],
  },
  {
    id: 2,
    image: "/images/blog2.png",
    title:
      "Navigation the Inclusion Body Challenge: Problems and Effective Strategies",
    paragraph:
      "Inclusion bodies (IB) are dense, aggregated, spherical and intracellular proteins found in the prokaryotic cytoplasm or periplasm ...",
    tags: [
      "Inclusion Bodies",
      "Protein Aggregation",
      "Cell Lysis",
      "Enzymatic Disrpution",
      "Fusion Tags",
    ],
  },
  {
    id: 3,
    image: "/images/blog3.png",
    title:
      "Navigation the Inclusion Body Challenge: Problems and Effective Strategies",
    paragraph:
      "Inclusion bodies (IB) are dense, aggregated, spherical and intracellular proteins found in the prokaryotic cytoplasm or periplasm ...",
    tags: [
      "Inclusion Bodies",
      "Protein Aggregation",
      "Cell Lysis",
      "Enzymatic Disrpution",
      "Fusion Tags",
    ],
  },
  {
    id: 4,
    image: "/images/Figure 5.png",
    title:
      "Navigation the Inclusion Body Challenge: Problems and Effective Strategies",
    paragraph:
      "Inclusion bodies (IB) are dense, aggregated, spherical and intracellular proteins found in the prokaryotic cytoplasm or periplasm ...",
    tags: [
      "Inclusion Bodies",
      "Protein Aggregation",
      "Cell Lysis",
      "Enzymatic Disrpution",
      "Fusion Tags",
    ],
  },
  {
    id: 5,
    image: "/images/Figure 6.png",
    title:
      "Navigation the Inclusion Body Challenge: Problems and Effective Strategies",
    paragraph:
      "Inclusion bodies (IB) are dense, aggregated, spherical and intracellular proteins found in the prokaryotic cytoplasm or periplasm ...",
    tags: [
      "Inclusion Bodies",
      "Protein Aggregation",
      "Cell Lysis",
      "Enzymatic Disrpution",
      "Fusion Tags",
    ],
  },
  {
    id: 6,
    image: "/images/blog3.png",
    title:
      "Navigation the Inclusion Body Challenge: Problems and Effective Strategies",
    paragraph:
      "Inclusion bodies (IB) are dense, aggregated, spherical and intracellular proteins found in the prokaryotic cytoplasm or periplasm ...",
    tags: [
      "Inclusion Bodies",
      "Protein Aggregation",
      "Cell Lysis",
      "Enzymatic Disrpution",
      "Fusion Tags",
    ],
  },
];

const RelatedArticles = () => {
  const [startIndex, setStartIndex] = useState(0);
  const articlesPerPage = 3;

  const next = () => {
    setStartIndex((prev) =>
      prev + articlesPerPage >= blogs.length ? 0 : prev + articlesPerPage
    );
  };

  const prev = () => {
    setStartIndex((prev) =>
      prev - articlesPerPage < 0
        ? blogs.length - articlesPerPage
        : prev - articlesPerPage
    );
  };

  const currentArticles = blogs.slice(startIndex, startIndex + articlesPerPage);
  console.log(currentArticles);

  return (
    <div className="py-10 px-6">
      <h2 className="text-2xl font-bold mb-6">You Might Also Like</h2>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {currentArticles.map((blog) => (
          <a
            key={blog.id}
            onClick={() => {
              window.open(`/singleArticle/${blog.id}`, "_blank");
            }}
            target="_blank"
            rel="noopener noreferrer"
            className="block border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden shadow-sm bg-white dark:bg-gray-800 hover:shadow-lg transition-shadow"
          >
            <img
              src={blog.image}
              alt={blog.title}
              className="w-full h-48 object-cover"
            />
            <div className="p-4">
              <h3 className="text-lg font-semibold mb-2">{blog.title}</h3>
              <p className="text-sm text-gray-600 dark:text-gray-300">
                {blog.paragraph}
              </p>
              <div className="flex flex-wrap gap-2 mt-4">
                {blog.tags.map((tag, i) => (
                  <span
                    key={i}
                    className="text-xs bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-white px-2 py-1 rounded-full border border-gray-300 dark:border-gray-600"
                  >
                    {tag}
                  </span>
                ))}
              </div>
            </div>
          </a>
        ))}
      </div>

      <div className="flex justify-between items-center mt-8">
        <button
          onClick={prev}
          className="flex items-center px-4 py-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-md"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Previous
        </button>
        <button
          onClick={next}
          className="flex items-center px-4 py-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-md"
        >
          Next
          <ArrowRight className="h-4 w-4 ml-2" />
        </button>
      </div>
    </div>
  );
};

export default RelatedArticles;
